/*
 * @Description:
 * @Autor: panmy
 * @Date: 2024-11-01 13:50:14
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 20:39:13
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  mtService = '/api/mtserviematters/mtServiceMattersField',
}

// 添加服务事项独立字段
export function addAloneField(data: any) {
  return defHttp.post({ url: Api.mtService + '/addAloneField', data });
}
// 查询服务事项独立字段列表
export function fetchAloneList() {
  return defHttp.get({ url: Api.mtService + '/list' });
}
// 编辑服务事项独立字段
export function editAloneField(data: any) {
  return defHttp.put({ url: Api.mtService + `/edit/${data.id}`, data });
}
// 删除服务事项独立字段/编辑字段
export function delAloneField(id) {
  return defHttp.delete({ url: Api.mtService + `/delete/${id}` });
}
// 编辑字段列表
export function getQueryAllList(id) {
  return defHttp.get({ url: Api.mtService + `/getList/${id}` });
}
// 保存/修改编辑字段
export function addMattersField(data: any) {
  return defHttp.post({ url: Api.mtService + '/addNew', data });
}
// 禁用/启用字段
export function handleEnable(id) {
  return defHttp.delete({ url: Api.mtService + `/unable/${id}` });
}
// 独立字段排序

export function handleOrder(data) {
  return defHttp.post({ url: `/api/knsDcwjTm/batchUpdateSort/${data.wjdm}`, data });
}
