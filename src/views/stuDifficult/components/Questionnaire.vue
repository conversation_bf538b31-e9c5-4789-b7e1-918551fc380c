<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-06 10:03:08
-->
<template>
  <a-row class="mb-100px">
    <a-col :span="14" :offset="5">
      <Spin :spinning="spinning">
        <Mtcn-Alert v-if="props.opType === 1" showIcon message="必须答完所有题目才能保存哦，请勿中途退出！"> </Mtcn-Alert>
        <BasicForm @register="registerForm" class="mt-20px"> </BasicForm>
      </Spin>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { RangePicker, Spin } from 'ant-design-vue';
  const props = defineProps({
    opType: {
      type: Number,
      default: 1, //1 学生 2 管理员
    },
  });
  const emit = defineEmits(['register', 'reload']);
  const id = ref('');
  const spinning = ref(false);
  const baseStore = useBaseStore();
  const api = useBaseApi('/api/knsPdxx');

  const { createMessage } = useMessage();
  const formArr = ref([]);
  const familyMembersForm = ref({});
  const formRefs = ref({});

  const schemas = ref([]);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    schemas: schemas.value,
    layout: 'vertical',
    labelWidth: '100%',
  });

  async function initForm() {
    spinning.value = true;
    const { data } = await api.request('get', '/api/knsDcwj/getDcwjList', { isFullPath: true });

    // 清空之前的数据
    questionDataMap.value.clear();

    (data?.tmVoList || []).forEach((item, index) => {
      // 保存题目信息到Map中
      questionDataMap.value.set(item.tmdm, {
        wjdm: item.wjdm,
        tmdm: item.tmdm,
        tmmc: item.tmmc,
        tmlx: item.tmlx,
        fz: item.fz,
        qtda: item.qtda,
        xxList: item.xxList || [],
      });

      let options = {};
      if (item.tmlx == 1 || item.tmlx == 2) {
        const optionItem = item.xxList.map(item => {
          return {
            fullName: item.xxmc,
            enCode: item.xxdm,
            tmdm: item.tmdm,
            fz: item.fz,
            qtda: item.qtda,
          };
        });
        options = {
          options: optionItem,
          fieldNames: { label: 'fullName', value: 'enCode' },
        };
      }

      schemas.value.push({
        field: `${item.wjdm}_${item.tmdm}`,
        label: `${index + 1}、${item.tmmc}`,
        component: item.tmtxfslx || 'Input',
        componentProps: { placeholder: item.tmlx == 3 ? '请输入' : '请选择', ...options, disabled: false },
        rules: [
          {
            required: item.sfbt || false,
            trigger: 'blur',
            message: `${item.tmlx == 3 ? '请输入' : '请选择'}`,
            type: { 1: 'string', 2: 'array', 3: 'string' }[item.tmlx],
          },
        ],
      });
    });
    // updateSchema(schemas.value)
    spinning.value = false;
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return false;

    // 组装保存数据
    const data = assembleSubmitData(values);
    console.log(data);
    await api.request('post', '/api/knsDcwjDt/save', { params: data, isFullPath: true });

    return values;
  }

  /**
   * 组装提交数据
   * @param values 表单验证后的数据，格式如：{ "**********_TM001": "A", "**********_TM002": ["B", "C"] }
   * @returns 组装后的数据，格式如：{ wjdm: '**********', dtmxVoList: [...] }
   */
  function assembleSubmitData(values) {
    const dtmxVoList = [];
    let wjdm = '';

    // 遍历表单数据
    Object.keys(values).forEach(fieldKey => {
      const fieldValue = values[fieldKey];

      // 解析字段名：格式为 ${wjdm}_${tmdm}
      const fieldParts = fieldKey.split('_');
      if (fieldParts.length >= 2) {
        const currentWjdm = fieldParts[0];
        const tmdm = fieldParts[1];

        // 设置问卷代码（取第一个即可，因为同一份问卷的wjdm都相同）
        if (!wjdm) {
          wjdm = currentWjdm;
        }

        // 从initForm中的数据找到对应的题目信息
        const questionInfo = findQuestionInfo(tmdm);
        debugger;
        if (!questionInfo) return;

        // 根据题目类型处理答案
        if (Array.isArray(fieldValue)) {
          // 多选题（tmlx = 2）：每个选项创建一条记录，使用外层的fz
          fieldValue.forEach(selectedValue => {
            // selectedValue 是选项代码（xxdm）
            debugger;
            const optionInfo = findOptionInfo(tmdm, selectedValue);
            dtmxVoList.push({
              xxdm: selectedValue, // 选项代码
              tmdm: tmdm,
              qtda: questionInfo.qtda,
              fz: optionInfo.fz,
            });
          });
        } else if (fieldValue) {
          // 单选题（tmlx = 1）或简答题（tmlx = 3）：创建一条记录
          if (questionInfo.tmlx == 1) {
            // 单选题：fieldValue 是选项代码（xxdm），使用选项的fz
            const optionInfo = findOptionInfo(tmdm, fieldValue);
            dtmxVoList.push({
              xxdm: fieldValue, // 选项代码
              tmdm: tmdm,
              qtda: fieldValue, // 对于选择题，qtda 通常也是选项代码
              fz: optionInfo?.fz, // 优先使用选项的fz，找不到则用题目的fz
            });
          } else if (questionInfo.tmlx == 3) {
            // 简答题：fieldValue 是用户输入的文本
            dtmxVoList.push({
              xxdm: '', // 简答题没有选项代码
              tmdm: tmdm,
              qtda: fieldValue, // 简答题的答案内容
              fz: questionInfo.fz, // 简答题使用题目的fz
            });
          }
        }
      }
    });

    return {
      wjdm,
      dtmxVoList,
    };
  }

  /**
   * 查找题目信息（需要在initForm中保存题目数据）
   * @param tmdm 题目代码
   * @returns 题目信息
   */
  const questionDataMap = ref(new Map()); // 存储题目信息的Map

  function findQuestionInfo(tmdm) {
    return questionDataMap.value.get(tmdm);
  }

  /**
   * 从schemas中查找选项信息
   * @param tmdm 题目代码
   * @param selectedValue 选择的值
   * @returns 选项信息
   */
  function findOptionInfo(tmdm, selectedValue) {
    // 遍历schemas找到对应题目的选项
    for (const schema of schemas.value) {
      if (schema.componentProps?.options) {
        const option = schema.componentProps.options.find(opt => opt.enCode === selectedValue && opt.tmdm === tmdm);
        if (option) {
          return {
            qtda: selectedValue, // 选项代码作为其他答案
            fz: option.fz,
          };
        }
      }
    }
    return null;
  }

  defineExpose({
    handleSubmit,
  });
  onMounted(() => {
    initForm();
    if (props.opType === 2) {
      const updates = schemas.value.map((item, index) => ({
        field: item.field,
        componentProps: {
          ...item.componentProps,
          disabled: index !== 0,
        },
      }));
      updateSchema(updates);
    }
  });
</script>
