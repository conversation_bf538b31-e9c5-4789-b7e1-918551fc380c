<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #headerTop>
            <Mtcn-Alert v-if="settingData?.bjgsksrq" showIcon :message="`您目前还不是困难生！申请时间为：${settingData?.bjgsksrq} ~ ${settingData?.bjgsjsrq}`">
            </Mtcn-Alert>
          </template>
          <template #tableTitle>
            <!-- 按钮组区 -->
            <a-button v-if="settingData.sfksq || false" type="primary" @click="addOrUpdateHandle()">申请</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <!-- 审核状态列 -->
            <template v-if="column.key === 'shzt'">
              <MtcnTextTag :content="getFlowStatusContent(record.status)" :color="getFlowStatusColor(record.shzt)" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 申请 -->
    <AddForm @register="registerForm" @reload="reload" />
    <QuestionForm @register="registerQuestionForm" @reload="reload" @finish="handleQuestionFinish" />
  </div>
</template>

<script lang="ts" setup>
  import { useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { onMounted, ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import AddForm from './components/AddForm.vue';
  import QuestionForm from './components/QuestionForm.vue';
  import { useBaseStore } from '@/store/modules/base';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { useDefineSetting } from '@/hooks/setting/useDefineSetting';
  import { getKnsSzOne } from '@/api/stuDifficult/knsSz';

  const { createMessage, createConfirm } = useMessage();
  const baseStore = useBaseStore();
  const api = useBaseApi('/api/knsPdxx');
  const [registerForm, { openPopup: openFormPopup }] = usePopup();
  const [registerQuestionForm, { openPopup: openQuestionFormPopup }] = usePopup();
  const { flowStatusList, flowUrgentList, getUrgentText, getUrgentTextColor, getFlowStatusContent, getFlowStatusColor } = useDefineSetting();

  // 存储设置数据
  const settingData = ref({});

  // 计算属性：判断当前时间是否在申请时间范围内
  const isInApplicationPeriod = ref(true);

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '姓名',
      dataIndex: 'xm',
      width: 80,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学号',
      dataIndex: 'xsbh',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学年',
      dataIndex: 'pdxn',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '困难生类型',
      dataIndex: 'knlxmc',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '申请时间',
      dataIndex: 'sqsj',
      width: 150,
      resizable: true,
      ellipsis: true,
    },

    {
      title: '院系',
      dataIndex: 'dwmc',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'zymc',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'bjmc',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '现在年级',
      dataIndex: 'xznj',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '审核状态',
      dataIndex: 'shzt',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
      fixed: 'right',
    },
  ];

  // 注册表格
  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    api: params => api.getList({ params }),
    columns,
    useSearchForm: true,
    formConfig: getFormConfig(),
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    rowSelection: {
      type: 'checkbox',
    },
  });

  function getFormConfig(): Partial<FormProps> {
    return {
      schemas: [
        {
          field: 'keyword',
          label: '关键词',
          component: 'Input',
          componentProps: {
            placeholder: '请输入学号、姓名搜索',
            submitOnPressEnter: true,
          },
        },
        {
          field: 'pdxn',
          label: '评定学年',
          component: 'Select',
          componentProps: {
            placeholder: '请选择评定学年',
          },
        },
      ],
    };
  }

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '申请表',
        onClick: () => {
          addOrUpdateHandle(record);
        },
      },
      {
        label: '问卷结果',
        ifShow: () => record.wjdm,
        onClick: () => {
          openQuestionForm(true, { ...record, opType: 2 });
        },
      },
      {
        label: '删除',
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
    ];
  }

  // 单个删除
  async function handleDelete(id) {
    try {
      const { msg } = await api.remove({ params: { id } });
      createMessage.success(msg || '删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
      createMessage.error('删除失败');
    }
  }

  const applyType = ref(3);
  function addOrUpdateHandle(id = undefined) {
    if (applyType.value == 3) {
      openQuestionFormPopup(true, { id });
    } else {
      openFormPopup(true, { id });
    }
  }
  function handleQuestionFinish() {
    openFormPopup(true, {});
  }

  async function getOptions() {
    // 设置评定学年选项
    const academicYears = await baseStore.getDictionaryData('xn');
    getForm().updateSchema({
      field: 'pdxn',
      componentProps: {
        options: academicYears,
        fieldNames: { label: 'fullName', value: 'enCode' },
      },
    });
  }

  async function getSettingData() {
    const res = await getKnsSzOne();
    if (res.data) {
      // 处理公示日期数据，将开始和结束时间合并为数组
      const formData = { ...res.data };

      if (formData.sqksrq && formData.sqjsrq) {
        formData.dateRange = [formData.sqksrq, formData.sqjsrq];
      }

      // 存储设置数据用于判断申请时间
      settingData.value = formData;
    }
  }

  onMounted(() => {
    getSettingData();
    getOptions();
  });
</script>
